{"name": "scheduler-test", "version": "1.0.0", "description": "Two server scheduler test environment", "main": "test.js", "scripts": {"start": "node start-servers.js", "server1": "node server1.js", "server2": "node server2.js", "test": "node test.js", "dev": "node start-servers.js", "web": "node web-server.js", "demo": "concurrently \"npm start\" \"npm run web\"", "all": "concurrently \"npm start\" \"npm run web\""}, "dependencies": {"express": "^4.18.2", "node-fetch": "^3.3.2"}, "devDependencies": {"concurrently": "^9.2.0"}, "type": "module"}