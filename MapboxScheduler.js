// MapboxScheduler.js - 专为Mapbox设计的调度器，使用PerformanceObserver

// 滑动平均窗口大小
const MOVING_AVERAGE_WINDOW = 100
// 探测超时时间 (ms)
const PROBE_TIMEOUT = 2000
// 失败惩罚时间 (ms)
const FAILURE_PENALTY = 5000
// 性能数据过期时间 (ms) - 5分钟
const PERFORMANCE_DATA_EXPIRY = 5 * 60 * 1000
// 最大无响应时间 (ms) - 超过此时间清空历史数据
const MAX_STALE_TIME = 30 * 1000

export default class MapboxScheduler {
  constructor(urlA, urlB, options = {}) {
    this.serverA = {
      name: 'A',
      url: urlA,
      isAvailable: true,
      responseTimes: [],
      avgResponseTime: Infinity,
      performanceEntries: [], // 存储详细的性能数据
      lastResponseTime: null, // 最后一次响应时间戳
      dataCreatedAt: Date.now() // 数据创建时间
    }
    this.serverB = {
      name: 'B',
      url: urlB,
      isAvailable: true,
      responseTimes: [],
      avgResponseTime: Infinity,
      performanceEntries: [],
      lastResponseTime: null,
      dataCreatedAt: Date.now()
    }

    // 调度选项
    this.options = {
      loadBalanceMode: options.loadBalanceMode || 'performance', // 'performance' | 'round-robin' | 'weighted'
      performanceThreshold: options.performanceThreshold || 50, // 性能差异阈值(ms)
      ...options
    }

    // 轮询计数器
    this.roundRobinCounter = 0

    // 初始化全局性能观察器
    this.initGlobalPerformanceObserver()
  }

  /**
   * 初始化全局性能观察器，监控所有网络请求
   */
  initGlobalPerformanceObserver() {
    if (typeof PerformanceObserver === 'undefined') {
      console.warn('PerformanceObserver not supported in this environment')
      return
    }

    // 监控资源加载性能
    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        // 检查是否是我们的瓦片请求
        if (
          entry.name.includes(this.serverA.url.split('{')[0]) ||
          entry.name.includes(this.serverB.url.split('{')[0])
        ) {
          this.processResourceEntry(entry)
        }
      })
    })

    // 监控用户自定义测量
    const measureObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        if (entry.name.startsWith('tile-request-')) {
          this.processMeasureEntry(entry)
        }
      })
    })

    try {
      resourceObserver.observe({ entryTypes: ['resource'] })
      measureObserver.observe({ entryTypes: ['measure'] })
      console.log('✅ PerformanceObserver initialized for Mapbox tile monitoring')
    } catch (error) {
      console.warn('Failed to initialize PerformanceObserver:', error)
    }
  }

  /**
   * 处理资源性能条目
   */
  processResourceEntry(entry) {
    const serverName = this.identifyServer(entry.name)
    if (!serverName) return

    const server = serverName === 'A' ? this.serverA : this.serverB

    // 计算各种性能指标
    const performanceData = {
      url: entry.name,
      duration: entry.duration,
      responseStart: entry.responseStart,
      responseEnd: entry.responseEnd,
      transferSize: entry.transferSize || 0,
      encodedBodySize: entry.encodedBodySize || 0,
      decodedBodySize: entry.decodedBodySize || 0,
      timestamp: entry.startTime
    }

    server.performanceEntries.push(performanceData)

    // 保持性能条目数组大小
    if (server.performanceEntries.length > MOVING_AVERAGE_WINDOW * 2) {
      server.performanceEntries.shift()
    }

    // 记录响应时间
    this.recordResult(serverName, entry.duration)

    console.log(`📊 Resource loaded from Server ${serverName}:`, {
      duration: `${entry.duration.toFixed(2)}ms`,
      size: `${(entry.transferSize / 1024).toFixed(2)}KB`,
      url: entry.name.substring(entry.name.lastIndexOf('/') + 1)
    })
  }

  /**
   * 处理自定义测量条目
   */
  processMeasureEntry(entry) {
    const serverName = entry.name.includes('-A-') ? 'A' : 'B'
    this.recordResult(serverName, entry.duration)

    console.log(`⏱️  Custom measure for Server ${serverName}: ${entry.duration.toFixed(2)}ms`)
  }

  /**
   * 识别请求来自哪个服务器
   */
  identifyServer(url) {
    const serverABase = this.serverA.url.split('{')[0]
    const serverBBase = this.serverB.url.split('{')[0]

    if (url.includes(serverABase)) return 'A'
    if (url.includes(serverBBase)) return 'B'
    return null
  }

  /**
   * 检查并清理过期的性能数据
   */
  _checkAndCleanStaleData(server) {
    const now = Date.now()

    // 检查数据是否过期（超过5分钟）
    if (now - server.dataCreatedAt > PERFORMANCE_DATA_EXPIRY) {
      console.log(`🧹 服务器${server.name}性能数据已过期，清空重新计算`)
      this._resetServerData(server)
      return
    }

    // 检查是否长时间无响应（超过30秒）
    if (server.lastResponseTime && now - server.lastResponseTime > MAX_STALE_TIME) {
      console.log(`⏰ 服务器${server.name}长时间无响应，清空历史数据`)
      this._resetServerData(server)
      return
    }
  }

  /**
   * 重置服务器数据
   */
  _resetServerData(server) {
    server.responseTimes = []
    server.avgResponseTime = Infinity
    server.performanceEntries = []
    server.lastResponseTime = null
    server.dataCreatedAt = Date.now()
    server.isAvailable = true // 重置为可用状态，给服务器重新证明的机会
  }

  /**
   * 更新指定服务器的平均响应时间
   */
  _updateAvgResponseTime(server) {
    if (server.responseTimes.length === 0) {
      server.avgResponseTime = Infinity
      return
    }
    const sum = server.responseTimes.reduce((a, b) => a + b, 0)
    server.avgResponseTime = sum / server.responseTimes.length
  }

  /**
   * 记录一次请求结果
   */
  recordResult(serverName, time) {
    const server = serverName === 'A' ? this.serverA : this.serverB
    if (!server) return

    // 检查并清理过期数据
    this._checkAndCleanStaleData(server)

    if (time !== null && typeof time === 'number') {
      // 成功
      server.responseTimes.push(time)
      if (server.responseTimes.length > MOVING_AVERAGE_WINDOW) {
        server.responseTimes.shift()
      }
      this._updateAvgResponseTime(server)
      server.isAvailable = true
      server.lastResponseTime = Date.now() // 更新最后响应时间
      console.log(`Server ${server.name} success. Avg: ${server.avgResponseTime.toFixed(2)}ms`)
    } else {
      // 失败
      server.avgResponseTime += FAILURE_PENALTY
      server.isAvailable = false
      server.lastResponseTime = Date.now() // 即使失败也更新时间戳
      console.error(`Server ${server.name} failure. Marked as unavailable.`)
    }
  }

  /**
   * 选择当前最优的服务器
   */
  getBestServer() {
    // 首先检查可用性
    if (!this.serverA.isAvailable && this.serverB.isAvailable) {
      console.log('Server A unavailable, choosing B')
      return this.serverB
    }
    if (!this.serverB.isAvailable && this.serverA.isAvailable) {
      console.log('Server B unavailable, choosing A')
      return this.serverA
    }
    if (!this.serverA.isAvailable && !this.serverB.isAvailable) {
      console.warn('Both servers unavailable! Trying A as fallback.')
      return this.serverA
    }

    // 根据负载均衡模式选择服务器
    switch (this.options.loadBalanceMode) {
      case 'round-robin':
        return this.getRoundRobinServer()

      case 'weighted':
        return this.getWeightedServer()

      case 'performance':
      default:
        return this.getPerformanceBasedServer()
    }
  }

  /**
   * 基于性能选择服务器
   */
  getPerformanceBasedServer() {
    const timeDiff = Math.abs(this.serverA.avgResponseTime - this.serverB.avgResponseTime)

    // 如果性能差异小于阈值，使用轮询
    if (timeDiff < this.options.performanceThreshold) {
      console.log(
        `Performance difference (${timeDiff.toFixed(2)}ms) below threshold, using round-robin`
      )
      return this.getRoundRobinServer()
    }

    // 否则选择性能更好的服务器
    if (this.serverA.avgResponseTime <= this.serverB.avgResponseTime) {
      console.log(
        `Choosing A (${this.serverA.avgResponseTime.toFixed(
          2
        )}ms) over B (${this.serverB.avgResponseTime.toFixed(2)}ms)`
      )
      return this.serverA
    } else {
      console.log(
        `Choosing B (${this.serverB.avgResponseTime.toFixed(
          2
        )}ms) over A (${this.serverA.avgResponseTime.toFixed(2)}ms)`
      )
      return this.serverB
    }
  }

  /**
   * 轮询选择服务器
   */
  getRoundRobinServer() {
    this.roundRobinCounter++
    const server = this.roundRobinCounter % 2 === 0 ? this.serverA : this.serverB
    console.log(`Round-robin: choosing ${server.name} (counter: ${this.roundRobinCounter})`)
    return server
  }

  /**
   * 基于权重选择服务器（响应时间越短权重越高）
   */
  getWeightedServer() {
    const weightA = this.serverA.avgResponseTime === Infinity ? 0 : 1 / this.serverA.avgResponseTime
    const weightB = this.serverB.avgResponseTime === Infinity ? 0 : 1 / this.serverB.avgResponseTime
    const totalWeight = weightA + weightB

    if (totalWeight === 0) {
      return this.getRoundRobinServer()
    }

    const random = Math.random()
    const threshold = weightA / totalWeight

    const server = random < threshold ? this.serverA : this.serverB
    console.log(
      `Weighted selection: choosing ${server.name} (weightA: ${(
        (weightA / totalWeight) *
        100
      ).toFixed(1)}%)`
    )
    return server
  }

  /**
   * 探测两个服务器的性能
   */
  async probeServers(z = 0, x = 0, y = 0) {
    console.log('开始探测两个服务器...')

    const probeServer = async (server) => {
      const url = server.url.replace('{z}', z).replace('{x}', x).replace('{y}', y)
      const measureName = `probe-${server.name}-${z}-${x}-${y}`

      // 开始性能测量
      performance.mark(`${measureName}-start`)

      try {
        // 创建AbortController用于超时控制
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), PROBE_TIMEOUT)

        const response = await fetch(url, {
          cache: 'no-store',
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        // 结束性能测量
        performance.mark(`${measureName}-end`)
        performance.measure(measureName, `${measureName}-start`, `${measureName}-end`)

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`)
        }

        return { server: server.name, success: true }
      } catch (error) {
        // 即使失败也要清理标记
        performance.mark(`${measureName}-end`)
        performance.measure(measureName, `${measureName}-start`, `${measureName}-end`)

        this.recordResult(server.name, null)

        return { server: server.name, success: false, error: error.message }
      } finally {
        // 清理性能标记
        performance.clearMarks(`${measureName}-start`)
        performance.clearMarks(`${measureName}-end`)
      }
    }

    // 同时探测两个服务器
    const results = await Promise.allSettled([probeServer(this.serverA), probeServer(this.serverB)])

    console.log('服务器探测完成')
    return results.map((result) => result.value || { success: false, error: 'Promise rejected' })
  }

  /**
   * 手动测量瓦片请求性能
   */
  async measureTileRequest(z, x, y) {
    const bestServer = this.getBestServer()
    const url = bestServer.url.replace('{z}', z).replace('{x}', x).replace('{y}', y)
    const measureName = `tile-request-${bestServer.name}-${z}-${x}-${y}`

    // 开始性能测量
    performance.mark(`${measureName}-start`)

    try {
      const response = await fetch(url, { cache: 'no-store' })

      // 结束性能测量
      performance.mark(`${measureName}-end`)
      performance.measure(measureName, `${measureName}-start`, `${measureName}-end`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      return {
        server: bestServer.name,
        url: url,
        success: true,
        data: await response.blob()
      }
    } catch (error) {
      // 即使失败也要清理标记
      performance.mark(`${measureName}-end`)
      performance.measure(measureName, `${measureName}-start`, `${measureName}-end`)

      this.recordResult(bestServer.name, null)

      return {
        server: bestServer.name,
        url: url,
        success: false,
        error: error.message
      }
    } finally {
      // 清理性能标记
      performance.clearMarks(`${measureName}-start`)
      performance.clearMarks(`${measureName}-end`)
    }
  }

  /**
   * 手动清理所有服务器的性能数据
   */
  clearPerformanceData() {
    console.log('🧹 手动清理所有服务器性能数据')
    this._resetServerData(this.serverA)
    this._resetServerData(this.serverB)
  }

  /**
   * 清理指定服务器的性能数据
   */
  clearServerData(serverName) {
    const server = serverName === 'A' ? this.serverA : this.serverB
    if (server) {
      console.log(`🧹 清理服务器${serverName}的性能数据`)
      this._resetServerData(server)
    }
  }

  /**
   * 获取性能统计信息
   */
  getPerformanceStats() {
    // 在获取统计信息时也检查数据是否过期
    this._checkAndCleanStaleData(this.serverA)
    this._checkAndCleanStaleData(this.serverB)

    const now = Date.now()
    return {
      serverA: {
        name: this.serverA.name,
        isAvailable: this.serverA.isAvailable,
        avgResponseTime: this.serverA.avgResponseTime,
        totalRequests: this.serverA.performanceEntries.length,
        recentEntries: this.serverA.performanceEntries.slice(-5),
        dataAge: Math.floor((now - this.serverA.dataCreatedAt) / 1000), // 数据年龄（秒）
        lastResponseAge: this.serverA.lastResponseTime
          ? Math.floor((now - this.serverA.lastResponseTime) / 1000)
          : null
      },
      serverB: {
        name: this.serverB.name,
        isAvailable: this.serverB.isAvailable,
        avgResponseTime: this.serverB.avgResponseTime,
        totalRequests: this.serverB.performanceEntries.length,
        recentEntries: this.serverB.performanceEntries.slice(-5),
        dataAge: Math.floor((now - this.serverB.dataCreatedAt) / 1000),
        lastResponseAge: this.serverB.lastResponseTime
          ? Math.floor((now - this.serverB.lastResponseTime) / 1000)
          : null
      }
    }
  }
}
