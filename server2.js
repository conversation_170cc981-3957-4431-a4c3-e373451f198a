// server2.js - 模拟服务器B
import express from 'express'

const app = express()
const PORT = 3002

// 添加CORS中间件
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept')
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200)
  }
  next()
})

// 模拟不同的响应时间和可用性
let responseDelay = 200 // 基础延迟，比服务器A稍慢
let isHealthy = true
let requestCount = 0

// 中间件：添加延迟
const addDelay = (req, res, next) => {
  setTimeout(() => {
    next()
  }, responseDelay + Math.random() * 100) // 添加随机延迟
}

// 健康检查端点
app.get('/health', (req, res) => {
  if (isHealthy) {
    res.json({ status: 'healthy', server: 'B', port: PORT })
  } else {
    res.status(500).json({ status: 'unhealthy', server: 'B', port: PORT })
  }
})

// 模拟瓦片服务端点 - 匹配调度器中的URL模式
app.get('/:z/:x/:y', addDelay, (req, res) => {
  requestCount++
  const { z, x, y } = req.params

  console.log(`Server B: Request ${requestCount} for tile ${z}/${x}/${y}`)

  if (!isHealthy) {
    return res.status(500).json({
      error: 'Server unavailable',
      server: 'B',
      tile: `${z}/${x}/${y}`
    })
  }

  // 模拟瓦片数据
  res.json({
    server: 'B',
    tile: `${z}/${x}/${y}`,
    timestamp: new Date().toISOString(),
    data: `Mock tile data from Server B for ${z}/${x}/${y}`
  })
})

// 控制端点 - 用于测试时调整服务器状态
app.post('/control/delay/:ms', (req, res) => {
  responseDelay = parseInt(req.params.ms)
  res.json({ message: `Server B delay set to ${responseDelay}ms` })
})

app.post('/control/health/:status', (req, res) => {
  isHealthy = req.params.status === 'true'
  res.json({ message: `Server B health set to ${isHealthy}` })
})

app.get('/status', (req, res) => {
  res.json({
    server: 'B',
    port: PORT,
    healthy: isHealthy,
    delay: responseDelay,
    requestCount: requestCount
  })
})

app.listen(PORT, () => {
  console.log(`🚀 Server B running on http://localhost:${PORT}`)
  console.log(`   Health: ${isHealthy ? '✅' : '❌'}`)
  console.log(`   Base delay: ${responseDelay}ms`)
})
