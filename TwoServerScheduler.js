// TwoServerScheduler.js

// 滑动平均窗口大小
const MOVING_AVERAGE_WINDOW = 10
// 探测超时时间 (ms)
const PROBE_TIMEOUT = 2000
// 失败惩罚时间 (ms)
const FAILURE_PENALTY = 5000

class TwoServerScheduler {
  constructor(urlA, urlB) {
    this.serverA = {
      name: 'A',
      url: urlA,
      isAvailable: true,
      responseTimes: [],
      avgResponseTime: Infinity
    }
    this.serverB = {
      name: 'B',
      url: urlB,
      isAvailable: true,
      responseTimes: [],
      avgResponseTime: Infinity
    }
  }

  /**
   * 更新指定服务器的平均响应时间
   * @param {object} server - serverA 或 serverB 对象
   */
  _updateAvgResponseTime(server) {
    if (server.responseTimes.length === 0) {
      server.avgResponseTime = Infinity
      return
    }
    const sum = server.responseTimes.reduce((a, b) => a + b, 0)
    server.avgResponseTime = sum / server.responseTimes.length
  }

  /**
   * 记录一次请求结果
   * @param {'A' | 'B'} serverName - 服务器名称
   * @param {number | null} time - 响应时间(ms)，如果失败则为 null
   */
  recordResult(serverName, time) {
    const server = serverName === 'A' ? this.serverA : this.serverB
    if (!server) return

    if (time !== null && typeof time === 'number') {
      // 成功
      server.responseTimes.push(time)
      if (server.responseTimes.length > MOVING_AVERAGE_WINDOW) {
        server.responseTimes.shift()
      }
      this._updateAvgResponseTime(server)
      server.isAvailable = true // 成功后标记为可用
      console.log(
        `Server ${server.name} success. New avg time: ${server.avgResponseTime.toFixed(2)}ms`
      )
    } else {
      // 失败
      server.avgResponseTime += FAILURE_PENALTY
      server.isAvailable = false // 直接标记为不可用
      console.error(`Server ${server.name} failure. Marked as unavailable.`)
    }
  }

  /**
   * 选择当前最优的服务器
   * @returns {object} The best server object
   */
  getBestServer() {
    // 如果 A 不可用，且 B 可用，则返回 B
    if (!this.serverA.isAvailable && this.serverB.isAvailable) {
      console.log('Server A unavailable, choosing B')
      return this.serverB
    }
    // 如果 B 不可用，且 A 可用，则返回 A
    if (!this.serverB.isAvailable && this.serverA.isAvailable) {
      console.log('Server B unavailable, choosing A')
      return this.serverA
    }
    // 如果都不可用，则尝试性地返回 A (或者可以抛出错误)
    if (!this.serverA.isAvailable && !this.serverB.isAvailable) {
      console.warn('Both servers are unavailable! Trying A as a fallback.')
      return this.serverA
    }

    // 如果都可用，则比较响应时间
    if (this.serverA.avgResponseTime <= this.serverB.avgResponseTime) {
      console.log(
        `Choosing A (${this.serverA.avgResponseTime.toFixed(
          2
        )}ms) over B (${this.serverB.avgResponseTime.toFixed(2)}ms)`
      )
      return this.serverA
    } else {
      console.log(
        `Choosing B (${this.serverB.avgResponseTime.toFixed(
          2
        )}ms) over A (${this.serverA.avgResponseTime.toFixed(2)}ms)`
      )
      return this.serverB
    }
  }

  /**
   * 对两个服务器进行探测
   * @param {string} probeTilePath - e.g., '0/0/0'
   */
  async probeServers(probeTilePath) {
    console.log('Starting server probes...')

    const probe = (server) => {
      const startTime = performance.now()
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), PROBE_TIMEOUT)

      const probeUrl = server.url.replace('{z}/{x}/{y}', probeTilePath)

      return fetch(probeUrl, { signal: controller.signal, cache: 'no-store' })
        .then((response) => {
          clearTimeout(timeoutId)
          if (!response.ok) throw new Error('Probe failed')
          const endTime = performance.now()
          this.recordResult(server.name, endTime - startTime)
        })
        .catch(() => {
          clearTimeout(timeoutId)
          this.recordResult(server.name, null)
        })
    }

    await Promise.allSettled([probe(this.serverA), probe(this.serverB)])
    console.log('Probing finished.')
  }
}
