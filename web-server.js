// web-server.js - 简单的Web服务器用于提供HTML示例
import express from 'express'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = 8888

// 设置静态文件服务
app.use(express.static(__dirname))

// 设置CORS头部，允许跨域请求
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept')
  next()
})

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(join(__dirname, 'mapbox-example.html'))
})

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', server: 'web-server', port: PORT })
})

app.listen(PORT, () => {
  console.log(`🌐 Web服务器运行在 http://localhost:${PORT}`)
  console.log(`📄 Mapbox示例页面: http://localhost:${PORT}`)
  console.log(`💡 请确保测试服务器也在运行 (npm start)`)
})
