<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mapbox调度器示例 - PerformanceObserver</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .server-status {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }
      .server-card {
        flex: 1;
        padding: 15px;
        border: 2px solid #ddd;
        border-radius: 8px;
        text-align: center;
      }
      .server-card.available {
        border-color: #4caf50;
        background-color: #f8fff8;
      }
      .server-card.unavailable {
        border-color: #f44336;
        background-color: #fff8f8;
      }
      .controls {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }
      button {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .btn-primary {
        background-color: #007bff;
        color: white;
      }
      .btn-success {
        background-color: #28a745;
        color: white;
      }
      .btn-warning {
        background-color: #ffc107;
        color: black;
      }
      .log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
      }
      .stats-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }
      .stats-table th,
      .stats-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }
      .stats-table th {
        background-color: #f2f2f2;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🗺️ Mapbox调度器示例 - PerformanceObserver</h1>
      <p>这个示例展示了如何使用PerformanceObserver来监控和优化Mapbox瓦片服务器的性能。</p>
    </div>

    <div class="container">
      <h2>服务器状态</h2>
      <div class="server-status">
        <div class="server-card" id="serverA">
          <h3>服务器 A</h3>
          <div>状态: <span id="statusA">检测中...</span></div>
          <div>平均响应时间: <span id="avgTimeA">-</span></div>
          <div>请求次数: <span id="requestCountA">0</span></div>
        </div>
        <div class="server-card" id="serverB">
          <h3>服务器 B</h3>
          <div>状态: <span id="statusB">检测中...</span></div>
          <div>平均响应时间: <span id="avgTimeB">-</span></div>
          <div>请求次数: <span id="requestCountB">0</span></div>
        </div>
      </div>
    </div>

    <div class="container">
      <h2>控制面板</h2>
      <div class="controls">
        <button class="btn-primary" onclick="probeServers()">探测两个服务器</button>
        <button class="btn-primary" onclick="loadTile()">加载瓦片</button>
        <button class="btn-success" onclick="loadMultipleTiles()">批量加载瓦片</button>
        <button class="btn-warning" onclick="simulateFailure()">模拟服务器故障</button>
        <button class="btn-primary" onclick="showPerformanceStats()">显示性能统计</button>
        <button class="btn-warning" onclick="clearPerformanceData()">清空性能数据</button>
        <button onclick="clearLog()">清空日志</button>
      </div>

      <div style="margin-top: 15px">
        <label style="margin-right: 10px"><b>负载均衡模式:</b></label>
        <select id="loadBalanceMode" onchange="changeLoadBalanceMode()" style="padding: 5px">
          <option value="performance">性能优先</option>
          <option value="round-robin">轮询</option>
          <option value="weighted">加权</option>
        </select>

        <div style="margin-top: 10px">
          <label>性能差异阈值: <span id="thresholdValue">30</span>ms</label>
          <input
            type="range"
            id="performanceThreshold"
            min="0"
            max="100"
            value="30"
            style="width: 200px; margin-left: 10px"
            oninput="updateThreshold(this.value)"
          />
        </div>
      </div>
    </div>

    <div class="container">
      <h2>性能统计</h2>
      <table class="stats-table" id="statsTable">
        <thead>
          <tr>
            <th>服务器</th>
            <th>状态</th>
            <th>平均响应时间</th>
            <th>总请求数</th>
            <th>最近5次请求</th>
          </tr>
        </thead>
        <tbody id="statsBody"></tbody>
      </table>
    </div>

    <div class="container">
      <h2>实时日志</h2>
      <div class="log" id="logContainer"></div>
    </div>

    <script type="module">
      import MapboxScheduler from './MapboxScheduler.js'

      // 创建调度器实例
      const scheduler = new MapboxScheduler(
        'http://localhost:3001/{z}/{x}/{y}',
        'http://localhost:3002/{z}/{x}/{y}',
        {
          loadBalanceMode: 'performance', // 'performance' | 'round-robin' | 'weighted'
          performanceThreshold: 30 // 性能差异阈值(ms)
        }
      )

      // 日志函数
      function log(message) {
        const logContainer = document.getElementById('logContainer')
        const timestamp = new Date().toLocaleTimeString()
        logContainer.textContent += `[${timestamp}] ${message}\n`
        logContainer.scrollTop = logContainer.scrollHeight
      }

      // 更新服务器状态显示
      function updateServerStatus() {
        const stats = scheduler.getPerformanceStats()

        // 更新服务器A
        const serverACard = document.getElementById('serverA')
        const statusA = document.getElementById('statusA')
        const avgTimeA = document.getElementById('avgTimeA')
        const requestCountA = document.getElementById('requestCountA')

        serverACard.className = `server-card ${
          stats.serverA.isAvailable ? 'available' : 'unavailable'
        }`
        statusA.textContent = stats.serverA.isAvailable ? '✅ 可用' : '❌ 不可用'
        avgTimeA.textContent =
          stats.serverA.avgResponseTime === Infinity
            ? '-'
            : `${stats.serverA.avgResponseTime.toFixed(2)}ms`
        requestCountA.textContent = stats.serverA.totalRequests

        // 更新服务器B
        const serverBCard = document.getElementById('serverB')
        const statusB = document.getElementById('statusB')
        const avgTimeB = document.getElementById('avgTimeB')
        const requestCountB = document.getElementById('requestCountB')

        serverBCard.className = `server-card ${
          stats.serverB.isAvailable ? 'available' : 'unavailable'
        }`
        statusB.textContent = stats.serverB.isAvailable ? '✅ 可用' : '❌ 不可用'
        avgTimeB.textContent =
          stats.serverB.avgResponseTime === Infinity
            ? '-'
            : `${stats.serverB.avgResponseTime.toFixed(2)}ms`
        requestCountB.textContent = stats.serverB.totalRequests
      }

      // 探测两个服务器
      window.probeServers = async function () {
        log('开始探测两个服务器...')

        try {
          const results = await scheduler.probeServers()
          results.forEach((result) => {
            if (result.success) {
              log(`✅ 服务器${result.server}探测成功`)
            } else {
              log(`❌ 服务器${result.server}探测失败: ${result.error || '未知错误'}`)
            }
          })
        } catch (error) {
          log(`❌ 服务器探测异常: ${error.message}`)
        }

        updateServerStatus()
      }

      // 加载单个瓦片
      window.loadTile = async function () {
        const z = Math.floor(Math.random() * 10)
        const x = Math.floor(Math.random() * Math.pow(2, z))
        const y = Math.floor(Math.random() * Math.pow(2, z))

        log(`开始加载瓦片 ${z}/${x}/${y}...`)

        try {
          const result = await scheduler.measureTileRequest(z, x, y)
          if (result.success) {
            log(`✅ 瓦片加载成功 - 服务器${result.server}: ${z}/${x}/${y}`)
          } else {
            log(`❌ 瓦片加载失败 - 服务器${result.server}: ${result.error}`)
          }
        } catch (error) {
          log(`❌ 瓦片加载异常: ${error.message}`)
        }

        updateServerStatus()
      }

      // 批量加载瓦片
      window.loadMultipleTiles = async function () {
        log('开始批量加载瓦片...')
        const promises = []

        for (let i = 0; i < 50; i++) {
          const z = Math.floor(Math.random() * 8)
          const x = Math.floor(Math.random() * Math.pow(2, z))
          const y = Math.floor(Math.random() * Math.pow(2, z))
          promises.push(scheduler.measureTileRequest(z, x, y))
        }

        try {
          const results = await Promise.allSettled(promises)
          results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
              const data = result.value
              if (data.success) {
                log(`✅ 批量请求${index + 1} - 服务器${data.server}成功`)
              } else {
                log(`❌ 批量请求${index + 1} - 服务器${data.server}失败: ${data.error}`)
              }
            } else {
              log(`❌ 批量请求${index + 1} - 异常: ${result.reason}`)
            }
          })
        } catch (error) {
          log(`❌ 批量加载异常: ${error.message}`)
        }

        updateServerStatus()
      }

      // 模拟服务器故障
      window.simulateFailure = async function () {
        log('模拟服务器A故障...')
        try {
          await fetch('http://localhost:3001/control/health/false', { method: 'POST' })
          log('✅ 服务器A已设置为不健康状态')

          // 等待2秒后恢复
          setTimeout(async () => {
            await fetch('http://localhost:3001/control/health/true', { method: 'POST' })
            log('✅ 服务器A已恢复健康状态')
            updateServerStatus()
          }, 2000)
        } catch (error) {
          log(`❌ 模拟故障失败: ${error.message}`)
        }
      }

      // 显示性能统计
      window.showPerformanceStats = function () {
        const stats = scheduler.getPerformanceStats()
        const tbody = document.getElementById('statsBody')

        tbody.innerHTML = `
                <tr>
                    <td>服务器 A</td>
                    <td>${stats.serverA.isAvailable ? '✅ 可用' : '❌ 不可用'}</td>
                    <td>${
                      stats.serverA.avgResponseTime === Infinity
                        ? '-'
                        : stats.serverA.avgResponseTime.toFixed(2) + 'ms'
                    }</td>
                    <td>${stats.serverA.totalRequests}</td>
                    <td>${stats.serverA.recentEntries
                      .map((e) => e.duration.toFixed(1) + 'ms')
                      .join(', ')}</td>
                </tr>
                <tr>
                    <td>服务器 B</td>
                    <td>${stats.serverB.isAvailable ? '✅ 可用' : '❌ 不可用'}</td>
                    <td>${
                      stats.serverB.avgResponseTime === Infinity
                        ? '-'
                        : stats.serverB.avgResponseTime.toFixed(2) + 'ms'
                    }</td>
                    <td>${stats.serverB.totalRequests}</td>
                    <td>${stats.serverB.recentEntries
                      .map((e) => e.duration.toFixed(1) + 'ms')
                      .join(', ')}</td>
                </tr>
            `

        log('📊 性能统计已更新')
      }

      // 清空日志
      window.clearLog = function () {
        document.getElementById('logContainer').textContent = ''
      }

      // 更改负载均衡模式
      window.changeLoadBalanceMode = function () {
        const mode = document.getElementById('loadBalanceMode').value
        scheduler.options.loadBalanceMode = mode
        log(`🔄 负载均衡模式已切换为: ${mode}`)
      }

      // 更新性能阈值
      window.updateThreshold = function (value) {
        scheduler.options.performanceThreshold = parseInt(value)
        document.getElementById('thresholdValue').textContent = value
        log(`⚙️ 性能差异阈值已设置为: ${value}ms`)
      }

      // 初始化
      log('🚀 Mapbox调度器已初始化')
      log('💡 请确保两个测试服务器正在运行 (npm start)')
      updateServerStatus()

      // 定期更新状态
      setInterval(updateServerStatus, 2000)
    </script>
  </body>
</html>
